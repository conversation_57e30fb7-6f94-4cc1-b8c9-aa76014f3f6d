# 2025/2026 Season Roster Cleanup Migration

## Overview
This migration archives (soft-deletes) all roster data for the 2025 and 2026 seasons to prepare for the new season. It ensures that Club Directors (CDs) don't have athletes or staff in their accounts and that events don't have any roster members for the 2025/2026 seasons.

## Migration File
`db/migrations/main/20250805130000_SW-4561.js`

## What Gets Archived

### 1. Event Roster Data (Open Rosters Only)
- **roster_athlete**: 2025 season athletes assigned to teams in 2026 events (only open rosters, not locked ones)
- **roster_staff_role**: 2025 season staff assigned to teams in 2026 events (only open rosters, not locked ones)

### 2. Club Master Data (All 2025 and 2026 Season Data)
- **master_athlete**: All athletes for 2025 and 2026 seasons across all clubs
- **master_staff**: All staff for 2025 and 2026 seasons across all clubs

### 3. Team Assignments for 2026 Season Teams
- **master_athlete.master_team_id**: Cleared (set to NULL) for 2025 season athletes assigned to 2026 season teams
- **master_staff_role**: Deleted (hard delete) for all staff role assignments to 2026 season teams

## Acceptance Criteria Verification

### Scenario 1: Open 2026 roster is cleared
- **Given**: A roster belongs to an event with season = 2026 AND roster is open (locked IS NULL OR locked IS NOT TRUE)
- **When**: The archive script/migration is executed
- **Then**: All matching rows for that roster are soft-deleted (deleted = NOW(), deleted_by_user = 'system_archive_2026')

### Scenario 2: Other seasons unaffected
- **Given**: A roster belongs to any season ≠ 2026 (open or blocked)
- **When**: Archive job runs
- **Then**: No athlete or staff rows are deleted for that roster

### Scenario 3: UI verification – team view
- **Given**: User (any role) opens the team-roster page for a 2026 event and roster that was open
- **When**: After archive job
- **Then**: Page displays "No rostered athletes / staff" (or equivalent empty-state) without error

## Key Features

### Soft Deletes
- Uses `deleted = NOW()` and `deleted_by_user = 'system_archive_2026'` instead of hard deletes
- Follows the pattern from `getTeamMembers` method in `ClubMembersController.js`
- Data can be restored if needed using the rollback migration

### Locked Roster Protection
- **Locked rosters are NOT affected** - only open rosters are cleared
- Locked rosters: `rt.locked IS TRUE`
- Open rosters: `rt.locked IS NULL OR rt.locked IS NOT TRUE`

### Selective Season Reset
- Clears event-specific roster data for open rosters
- Clears club master data only for teams with open rosters
- Protects master data for teams with locked rosters
- Ensures CDs start with clean accounts for teams with open rosters

## Verification Scripts

### Pre-Migration Verification
Run `scripts/verify-2026-roster-cleanup.sql` to see what will be affected:
- Counts of 2026 events
- Open vs locked roster teams
- Athletes and staff that will be archived
- Specific check for event 25997 (mentioned in requirements)
- Master athletes and staff counts

### Post-Migration Verification
Run `scripts/verify-2026-roster-cleanup-after.sql` to confirm results:
- Verify no active members remain in open 2026 rosters
- Verify locked rosters were not affected
- Verify other seasons were not affected
- Count archived records
- Verify master data was cleared

## Usage

### Running the Migration
```bash
npm run knex migrate:latest
```

### Rolling Back (if needed)
```bash
npm run knex migrate:rollback
```

### Verification
```bash
# Before migration
psql -d sw_dev -f scripts/verify-2026-roster-cleanup.sql

# After migration
psql -d sw_dev -f scripts/verify-2026-roster-cleanup-after.sql
```

## Important Notes

1. **Backup Recommended**: Although this uses soft deletes, consider backing up the database before running
2. **Event 25997**: Specifically mentioned in requirements - will be cleared if it has open rosters
3. **Seasons 2025/2026**: Affects both 2025 and 2026 season data, other seasons remain untouched
4. **Team Assignments**: 2025 season athletes assigned to 2026 season teams will have their team assignments cleared
5. **Locked Rosters**: Protected from deletion - only open rosters are cleared
6. **Rollback Available**: Migration can be rolled back to restore all archived data

## Technical Details

### Tables Affected
- `roster_athlete` (2025 season members in event-specific open rosters only)
- `roster_staff_role` (2025 season members in event-specific open rosters only)
- `master_athlete` (all 2025/2026 season data + master_team_id cleared for 2025 athletes assigned to 2026 teams)
- `master_staff` (all 2025/2026 season data)
- `master_staff_role` (hard deleted for 2026 season teams)

### Deletion Marker
All archived records are marked with:
- `deleted = NOW()`
- `deleted_by_user = 'system_archive_2025_2026'` (for master data and team assignments)
- `deleted_by_user = 'system_archive_2026'` (for event roster data)

This allows for easy identification and potential restoration of archived data.

## Master Data Relationship Logic

### How Master Records are Connected to Rosters
- **master_athlete** → **master_team** (via `master_team_id`)
- **master_staff** → **master_staff_role** → **master_team** (via `master_team_id`)
- **master_team** → **roster_team** (via `master_team_id`)
- **roster_team** → **event** (via `event_id`)

### Selection Criteria for Master Data
**Master Athletes:**
```sql
master_athlete.master_team_id IN (
    SELECT DISTINCT roster_team.master_team_id
    FROM roster_team
    INNER JOIN event ON event.event_id = roster_team.event_id
    WHERE event.season = 2026
    AND (roster_team.locked IS NULL OR roster_team.locked IS NOT TRUE)
)
```

**Master Staff:**
```sql
master_staff.master_staff_id IN (
    SELECT DISTINCT master_staff_role.master_staff_id
    FROM master_staff_role
    INNER JOIN roster_team ON roster_team.master_team_id = master_staff_role.master_team_id
    INNER JOIN event ON event.event_id = roster_team.event_id
    WHERE event.season = 2026
    AND (roster_team.locked IS NULL OR roster_team.locked IS NOT TRUE)
)
```

This ensures that only master records belonging to teams with open rosters are archived, while preserving master data for teams with locked rosters.
